﻿<UserControl x:Class="TD.Resources.UserControls.ValvesUserControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" xmlns:local3="clr-namespace:TD.Resources.UserControls" xmlns:local2="clr-namespace:TD.Resources" xmlns:local1="clr-namespace:TD"
             mc:Ignorable="d"
             Background="Transparent"
             >
    <Canvas  Width="248" Height="132">
        <local3:LAngleUserControl x:Name="LAngle" Panel.ZIndex="1"
                CornerDirection="RightTop"
                HorizontalLength="80"
                VerticalLength="40"
                Canvas.Left="60"
                Canvas.Top="20"
                Height="36"
                Width="101" />
        <local2:EllipseUserControl Panel.ZIndex="2" Canvas.Left="104" Canvas.Top="18" HorizontalAlignment="Center" VerticalAlignment="Top" />
        <local3:LAngleUserControl 
        CornerDirection="LeftTop"
        HorizontalLength="70"
        VerticalLength="40"
        Canvas.Left="31"
        Canvas.Top="20"
        Width="101" HorizontalAlignment="Left" VerticalAlignment="Top" Panel.ZIndex="1"/>
        <!--<local1:PipeUserControl Length="40" Orientation="Vertical" Canvas.Left="295" Canvas.Top="189" HorizontalAlignment="Center" VerticalAlignment="Top" />-->
    </Canvas>


</UserControl>
