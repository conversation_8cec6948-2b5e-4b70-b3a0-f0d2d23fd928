﻿<Page x:Class="TD.Pages.Page3BackUp"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:local="clr-namespace:TD.Pages" xmlns:local1="clr-namespace:TD" xmlns:local2="clr-namespace:TD.Resources" xmlns:local3="clr-namespace:TD.Resources.UserControls"
      mc:Ignorable="d" 
     d:DesignHeight="700" d:DesignWidth="1000"
        Title="ChamberPage" Background="#B1D2F2">

    <!-- 使用Viewbox实现响应式缩放 -->
    <Viewbox Stretch="Uniform" StretchDirection="Both">
        <Canvas Width="1200" Height="700" Name="PipelineCanvas" ScrollViewer.VerticalScrollBarVisibility="Auto" 
                ScrollViewer.HorizontalScrollBarVisibility="Auto">

            <!-- 外围边框 -->
            <Rectangle Width="1180" Height="680" Panel.ZIndex="-1"
                       Fill="Transparent" Stroke="Black" StrokeThickness="2" HorizontalAlignment="Left" VerticalAlignment="Top" Canvas.Left="10" Canvas.Top="-27"/>

            <!-- TM 容器 (左侧，带斜角底部) -->
            <Image Source="/Resources/Images/TMBackground.png" Height="220" Width="232" Canvas.Left="191" Canvas.Top="174" HorizontalAlignment="Left" VerticalAlignment="Top" Panel.ZIndex="1"/>
            <TextBlock Text="TM" Canvas.Left="260" Canvas.Top="236" FontSize="36" FontWeight="Bold" Panel.ZIndex="2"
                       HorizontalAlignment="Left"/>

            <!-- PM 容器 (右侧) -->
            <Image Source="/Resources/Images/PM.png" Height="110" Width="241" Canvas.Left="480" Canvas.Top="199" HorizontalAlignment="Left" VerticalAlignment="Top" ></Image>
            <TextBlock Text="PM" Canvas.Left="560" Canvas.Top="231" FontSize="36" FontWeight="Bold"/>

            <!-- LLA -->
            <Image Source="/Resources/Images/LL.png" Height="183" Width="183" Canvas.Left="137" Canvas.Top="328" HorizontalAlignment="Left" VerticalAlignment="Top" Panel.ZIndex="1"/>
            <TextBlock Text="LLA" Canvas.Left="182" Canvas.Top="407" FontSize="36" FontWeight="Bold" Panel.ZIndex="2"
            HorizontalAlignment="Left" RenderTransformOrigin="3.916,0.704"/>

            <!-- LLB -->
            <Image Source="/Resources/Images/LL.png" Height="183" Width="183" Canvas.Left="328" Canvas.Top="348" HorizontalAlignment="Left" VerticalAlignment="Top" Panel.ZIndex="1" RenderTransformOrigin="0.5,0.5">
                <Image.RenderTransform>
                    <TransformGroup>
                        <ScaleTransform/>
                        <SkewTransform/>
                        <RotateTransform Angle="155.214"/>
                        <TranslateTransform/>
                    </TransformGroup>
                </Image.RenderTransform>
            </Image>
            <TextBlock Text="LLB" Canvas.Left="391" Canvas.Top="403" FontSize="36" FontWeight="Bold" Panel.ZIndex="2"
 HorizontalAlignment="Left" VerticalAlignment="Center"/>

            <!-- PMN7/PMN8 -->
            <local1:PipeUserControl
                Length="400" Panel.ZIndex="-1"
                Orientation="Horizontal"
                Canvas.Left="684"
                Canvas.Top="290"/>

            <!-- PU12 左侧L角连接到TM -->
            <local3:LAngleUserControl
                CornerDirection="LeftBottom"
                HorizontalLength="200"
                VerticalLength="200"
                Canvas.Left="49"
                Canvas.Top="56"
                Height="213"
                Width="90" HorizontalAlignment="Left" VerticalAlignment="Top" />

            <!-- PU12 右侧L角连接到PM -->

            <!-- TM 到 PM 水平连接管路 -->
            <local1:PipeUserControl
                Length="120" Panel.ZIndex="-1"
                Orientation="Horizontal"
                Canvas.Left="363"
                Canvas.Top="221" HorizontalAlignment="Left" VerticalAlignment="Center"/>

            <!-- PM 右侧分子泵管路 -->
            <local1:PipeUserControl
                Length="360"
                Orientation="Horizontal"
                Canvas.Left="719"
                Canvas.Top="217" HorizontalAlignment="Left" VerticalAlignment="Center"/>
            <!-- LLA分子泵上面管路 -->
            <local3:LAngleUserControl
                CornerDirection="LeftTop"
                HorizontalLength="140"
                VerticalLength="220"
                Canvas.Left="24"
                Canvas.Top="362"
                Height="138"
                Width="108" HorizontalAlignment="Left" VerticalAlignment="Top"/>
            <!-- LLA分子泵管路 -->
            <local3:LAngleUserControl
                CornerDirection="LeftTop" 
                HorizontalLength="105"
                VerticalLength="160"
                Canvas.Left="41"
                Canvas.Top="399"
                Height="125"
                Width="80" HorizontalAlignment="Left" VerticalAlignment="Center"/>
            <!-- LLA分子泵阀门 -->
            <local2:EllipseUserControl Canvas.Left="112" Canvas.Top="397"  Panel.ZIndex="10" HorizontalAlignment="Left" VerticalAlignment="Top"/>
            <TextBlock Text="分子泵" Canvas.Left="109" Canvas.Top="379" FontSize="10" Foreground="Black" RenderTransformOrigin="1.487,-1.528" HorizontalAlignment="Left" VerticalAlignment="Top"/>
            <TextBlock Text="LLP10" Canvas.Left="47" Canvas.Top="379" FontSize="10" Foreground="Black" RenderTransformOrigin="1.487,-1.528" HorizontalAlignment="Left" VerticalAlignment="Center"/>
            <!--LLP10 输出 前级阀门-->
            <local2:EllipseUserControl Canvas.Left="56" Canvas.Top="397"  Panel.ZIndex="10" HorizontalAlignment="Center" VerticalAlignment="Top"/>

            <!--LLA分子泵下输出抽气阀-->
            <local1:PipeUserControl
            Length="90" Panel.ZIndex="1"
            Orientation="Horizontal"
            Canvas.Left="49"
            Canvas.Top="458" HorizontalAlignment="Left" VerticalAlignment="Top"/>
            <!-- 左侧小容器到右侧小容器连接 -->
            <local1:PipeUserControl
                Length="160"
                Orientation="Horizontal"
                Canvas.Left="692"
                Canvas.Top="593"/>

            <!-- 底部N2泵连接 -->
            <local1:PipeUserControl
                Length="100"
                Orientation="Horizontal"
                Canvas.Left="950"
                Canvas.Top="540"/>

            <!-- 分子泵连接 - TM左侧 -->



            <!-- 阀门和连接点 - 根据图片重新布置，确保阀门居中 -->

            <!-- 顶部PU12管路阀门 TP5 (居中) -->
            <local2:EllipseUserControl
                Canvas.Left="485" Canvas.Top="48"
                Panel.ZIndex="10" HorizontalAlignment="Left" VerticalAlignment="Center"/>

            <!-- TM 左侧分子泵阀门 -->
            <local2:EllipseUserControl
                Canvas.Left="144" Canvas.Top="254"
                Panel.ZIndex="1" HorizontalAlignment="Left" VerticalAlignment="Top"/>

            <!-- N2泵阀门 (居中) -->
            <local2:EllipseUserControl
                Width="12" Height="12"
                Canvas.Left="994" Canvas.Top="534"
                Panel.ZIndex="1"/>



            <!-- 标签文本 - 根据图片重新布置 -->

            <!-- 顶部标签 -->
            <TextBlock Text="PU12" Canvas.Left="148" Canvas.Top="27" FontSize="12" Foreground="Black" FontWeight="Bold"/>
            <TextBlock Text="TP5" Canvas.Left="465" FontSize="10" Foreground="Black" Canvas.Top="-5"/>

            <!-- TM 左侧分子泵标签 -->
            <TextBlock Text="分子泵" Canvas.Left="137" Canvas.Top="270" FontSize="10" Foreground="Blue" RenderTransformOrigin="1.487,-1.528"/>

            <!-- PM 右侧标签 -->
            <TextBlock Text="PU11" Canvas.Left="875" Canvas.Top="193" FontSize="10" Foreground="Black"/>
            <TextBlock Text="PMP8" Canvas.Left="669" Canvas.Top="138" FontSize="10" Foreground="Black" Panel.ZIndex="2"/>
            <TextBlock Text="PMP7" Canvas.Left="670" Canvas.Top="70" FontSize="10" Foreground="Black" Panel.ZIndex="2"/>
            <TextBlock Text="PMN8" Canvas.Left="844" Canvas.Top="238" FontSize="10" Foreground="Black"/>
            <TextBlock Text="PMN7" Canvas.Left="844" Canvas.Top="307" FontSize="10" Foreground="Black" HorizontalAlignment="Left" VerticalAlignment="Center"/>
            <TextBlock Text="LLM4" Canvas.Left="624" Canvas.Top="421" FontSize="10" Foreground="Black"/>
            <TextBlock Text="LLM6" Canvas.Left="611" Canvas.Top="440" FontSize="10" Foreground="Black"/>

            <!-- 压力表标签 -->
            <TextBlock Text="PT" Canvas.Left="726" Canvas.Top="234" FontSize="10" Foreground="Black"/>

            <!-- N2泵标签 -->
            <TextBlock Text="N2 60psi" Canvas.Left="1060" Canvas.Top="520" FontSize="10" Foreground="Blue"/>
            <!-- TM分子泵管道 -->
            <local3:LAngleUserControl
                CornerDirection="LeftTop"
                HorizontalLength="1034"
                VerticalLength="203"
                Canvas.Left="49"
                Canvas.Top="52"
                Height="173"
                Width="58" HorizontalAlignment="Left" VerticalAlignment="Top" Panel.ZIndex="1" />
            <local2:EllipseUserControl
                Canvas.Left="144" Canvas.Top="50"
                Panel.ZIndex="2" HorizontalAlignment="Center" VerticalAlignment="Top"  />
            <!--TM连接PU12管道-->
            <local1:PipeUserControl Length="120" Orientation="Vertical" Canvas.Left="363" Canvas.Top="54" HorizontalAlignment="Left" VerticalAlignment="Top" />
            <!--TM输出抽气阀门TP5/TP6-->
            <local3:ValvesUserControl Canvas.Left="380" Panel.ZIndex="1" Canvas.Top="-6" AllowDrop="True" Height="56" Width="163" HorizontalAlignment="Center" VerticalAlignment="Top"/>
            <TextBlock Text="TP6" Canvas.Left="465" FontSize="10" Foreground="Black" Canvas.Top="64" HorizontalAlignment="Center" VerticalAlignment="Top"/>
            <!--输出抽气阀门PM7/PM8-->
            <local3:LAngleUserControl
                CornerDirection="LeftTop"
                HorizontalLength="520"
                VerticalLength="80"
                Canvas.Left="560"
                Canvas.Top="128"
                Height="121"
                Width="90" HorizontalAlignment="Center" VerticalAlignment="Top" Panel.ZIndex="-1" />
            <!--PM输出抽气阀门PM7/PM8-->
            <local3:ValvesUserControl Canvas.Left="600" Panel.ZIndex="1" Canvas.Top="70" AllowDrop="True"  Width="166" RenderTransformOrigin="0.5,0.537" HorizontalAlignment="Left" VerticalAlignment="Top" Height="74"/>
            <local2:EllipseUserControl
                Canvas.Left="704" Canvas.Top="125"
                Panel.ZIndex="10" HorizontalAlignment="Center" VerticalAlignment="Top"/>
            <!--PM分子泵-->
            <local2:EllipseUserControl
                Canvas.Left="754" Canvas.Top="215"
                Panel.ZIndex="10" HorizontalAlignment="Left" VerticalAlignment="Center"/>
            <TextBlock Text="分子泵" Canvas.Left="742" Canvas.Top="199" FontSize="10" Foreground="Black" HorizontalAlignment="Left" VerticalAlignment="Top"/>
            <!--PU11-->
            <local2:EllipseUserControl Canvas.Left="876" Canvas.Top="215"  Panel.ZIndex="10" HorizontalAlignment="Center" VerticalAlignment="Top"/>
            <local3:ValvesUserControl Canvas.Left="771" Panel.ZIndex="1" Canvas.Top="232" AllowDrop="True"  Width="175" RenderTransformOrigin="0.5,0.537" Height="51" HorizontalAlignment="Left" VerticalAlignment="Top"/>
            <!--最右侧竖向管道-->
            <local3:LAngleUserControl CornerDirection="RightTop"
    HorizontalLength="40"
    VerticalLength="500"
    Canvas.Left="1041"
    Canvas.Top="52"
    Height="106"
    Width="121"  Panel.ZIndex="1" />
            <local2:EllipseUserControl Canvas.Left="876" Canvas.Top="288"  Panel.ZIndex="10" HorizontalAlignment="Center" VerticalAlignment="Top"/>
            <!--LL连接管2-->
            <local1:PipeUserControl
                Length="120" Panel.ZIndex="-1"
                Orientation="Horizontal"
                Canvas.Left="363"
                Canvas.Top="269" HorizontalAlignment="Left" VerticalAlignment="Center"/>
            <!--TN2/TN1-->
            <local3:VentUserControl Canvas.Left="686" Canvas.Top="280" HorizontalAlignment="Center" VerticalAlignment="Top" Width="291" Height="96" />
            <!--TN2/TN1连接管-->
            <local1:PipeUserControl
                Length="790"
                Orientation="Horizontal"
                Canvas.Left="290"
                Canvas.Top="358" HorizontalAlignment="Left" VerticalAlignment="Top" Panel.ZIndex="-1"/>




        </Canvas>
    </Viewbox>
</Page>
