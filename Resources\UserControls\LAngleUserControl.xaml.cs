using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace TD.Resources.UserControls
{
    /// <summary>
    /// LAngleUserControl.xaml 的交互逻辑
    /// </summary>
    public partial class LAngleUserControl : UserControl
    {
        public enum LAngleDirection
        {
            LeftTop,
            LeftBottom,
            RightTop,
            RightBottom
        }

        public LAngleUserControl()
        {
            InitializeComponent();
            UpdateGeometry();
        }
        public double VerticalPipeLeft => HorizontalLength - 6;
        // 依赖属性
        public static readonly DependencyProperty CornerDirectionProperty =
            DependencyProperty.Register(
                "CornerDirection",
                typeof(LAngleDirection),
                typeof(LAngleUserControl),
                new PropertyMetadata(LAngleDirection.RightTop, OnGeometryChanged));

        public static readonly DependencyProperty HorizontalLengthProperty =
            DependencyProperty.Register(
                "HorizontalLength",
                typeof(double),
                typeof(LAngleUserControl),
                new PropertyMetadata(0.0, OnGeometryChanged));

        public static readonly DependencyProperty VerticalLengthProperty =
            DependencyProperty.Register(
                "VerticalLength",
                typeof(double),
                typeof(LAngleUserControl),
                new PropertyMetadata(0.0, OnGeometryChanged));

        public static readonly DependencyProperty LAngleGeometryProperty =
            DependencyProperty.Register(
                "LAngleGeometry",
                typeof(Geometry),
                typeof(LAngleUserControl),
                new PropertyMetadata(null)
            );
        public static readonly DependencyProperty VAngleGeometryProperty =
           DependencyProperty.Register(
               "VAngleGeometry",
               typeof(Geometry),
               typeof(LAngleUserControl),
               new PropertyMetadata(null)
           );

        // 属性封装
        public LAngleDirection CornerDirection
        {
            get => (LAngleDirection)GetValue(CornerDirectionProperty);
            set => SetValue(CornerDirectionProperty, value);
        }

        public double HorizontalLength
        {
            get => (double)GetValue(HorizontalLengthProperty);
            set => SetValue(HorizontalLengthProperty, value);
        }

        public double VerticalLength
        {
            get => (double)GetValue(VerticalLengthProperty);
            set => SetValue(VerticalLengthProperty, value);
        }

        public Geometry LAngleGeometry
        {
            get => (Geometry)GetValue(LAngleGeometryProperty);
            set => SetValue(LAngleGeometryProperty, value);
        }

        public Geometry VAngleGeometry
        {
            get => (Geometry)GetValue(VAngleGeometryProperty);
            set => SetValue(VAngleGeometryProperty, value);
        }

        // 依赖属性变化时回调
        private static void OnGeometryChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is LAngleUserControl ctrl)
            {
                ctrl.UpdateGeometry();
            }
        }


        private void UpdateGeometry()
        {
            double width = HorizontalLength;
            double height = 8;
            double vwidth = 8;
            double vheight = VerticalLength;

            switch (CornerDirection)
            {
                case LAngleDirection.RightTop:
                    // 右上角L - 横向部分
                    var horizontalRect = new RectangleGeometry(new Rect(0, 0, width, height));
                    var horizontalPathFigure = new PathFigure
                    {
                        StartPoint = new Point(width - 6, height),
                        Segments = new PathSegmentCollection
                        {
                            new ArcSegment(new Point(width, 0), new Size(90, 90), 0, false, SweepDirection.Clockwise, true),
                            new LineSegment(new Point(width, height), true)
                        }
                    };
                    var horizontalPathGeometry = new PathGeometry(new[] { horizontalPathFigure });
                    var horizontalCombined = new CombinedGeometry(GeometryCombineMode.Exclude, horizontalRect, horizontalPathGeometry);
                    LAngleGeometry = horizontalCombined;

                    // 右上角L - 垂直部分
                    var verticalRect = new RectangleGeometry(new Rect(0, 0, vwidth, vheight));
                    var verticalPathFigure = new PathFigure
                    {
                        StartPoint = new Point(0, 6),
                        Segments = new PathSegmentCollection
                        {
                            new ArcSegment(new Point(6, 0), new Size(90, 90), 0, false, SweepDirection.Counterclockwise, true),
                            new LineSegment(new Point(0, 0), true)
                        }
                    };
                    var verticalPathGeometry = new PathGeometry(new[] { verticalPathFigure });
                    var verticalCombined = new CombinedGeometry(GeometryCombineMode.Exclude, verticalRect, verticalPathGeometry);
                    VAngleGeometry = verticalCombined;
                    break;

                case LAngleDirection.LeftTop:
                    // 左上角L - 横向部分
                    var leftTopHorizontalRect = new RectangleGeometry(new Rect(0, 0, width, height));
                    var leftTopHorizontalPathFigure = new PathFigure
                    {
                        StartPoint = new Point(6, height),
                        Segments = new PathSegmentCollection
                        {
                            new ArcSegment(new Point(0, 0), new Size(90, 90), 0, false, SweepDirection.Counterclockwise, true),
                            new LineSegment(new Point(0, height), true)
                        }
                    };
                    var leftTopHorizontalPathGeometry = new PathGeometry(new[] { leftTopHorizontalPathFigure });
                    var leftTopHorizontalCombined = new CombinedGeometry(GeometryCombineMode.Exclude, leftTopHorizontalRect, leftTopHorizontalPathGeometry);
                    LAngleGeometry = leftTopHorizontalCombined;

                    // 左上角L - 垂直部分
                    var leftTopVerticalRect = new RectangleGeometry(new Rect(0, 0, vwidth, vheight));
                    var leftTopVerticalPathFigure = new PathFigure
                    {
                        StartPoint = new Point(vwidth, 6),
                        Segments = new PathSegmentCollection
                        {
                            new ArcSegment(new Point(vwidth - 6, 0), new Size(90, 90), 0, false, SweepDirection.Clockwise, true),
                            new LineSegment(new Point(vwidth, 0), true)
                        }
                    };
                    var leftTopVerticalPathGeometry = new PathGeometry(new[] { leftTopVerticalPathFigure });
                    var leftTopVerticalCombined = new CombinedGeometry(GeometryCombineMode.Exclude, leftTopVerticalRect, leftTopVerticalPathGeometry);
                    VAngleGeometry = leftTopVerticalCombined;
                    break;

                case LAngleDirection.RightBottom:
                    // 右下角L - 横向部分
                    var rightBottomHorizontalRect = new RectangleGeometry(new Rect(0, 0, width, height));
                    var rightBottomHorizontalPathFigure = new PathFigure
                    {
                        StartPoint = new Point(width - 6, 0),
                        Segments = new PathSegmentCollection
                        {
                            new ArcSegment(new Point(width, height), new Size(90, 90), 0, false, SweepDirection.Counterclockwise, true),
                            new LineSegment(new Point(width, 0), true)
                        }
                    };
                    var rightBottomHorizontalPathGeometry = new PathGeometry(new[] { rightBottomHorizontalPathFigure });
                    var rightBottomHorizontalCombined = new CombinedGeometry(GeometryCombineMode.Exclude, rightBottomHorizontalRect, rightBottomHorizontalPathGeometry);
                    LAngleGeometry = rightBottomHorizontalCombined;

                    // 右下角L - 垂直部分
                    var rightBottomVerticalRect = new RectangleGeometry(new Rect(0, 0, vwidth, vheight));
                    var rightBottomVerticalPathFigure = new PathFigure
                    {
                        StartPoint = new Point(0, vheight - 6),
                        Segments = new PathSegmentCollection
                        {
                            new ArcSegment(new Point(6, vheight), new Size(90, 90), 0, false, SweepDirection.Clockwise, true),
                            new LineSegment(new Point(0, vheight), true)
                        }
                    };
                    var rightBottomVerticalPathGeometry = new PathGeometry(new[] { rightBottomVerticalPathFigure });
                    var rightBottomVerticalCombined = new CombinedGeometry(GeometryCombineMode.Exclude, rightBottomVerticalRect, rightBottomVerticalPathGeometry);
                    VAngleGeometry = rightBottomVerticalCombined;
                    break;

                case LAngleDirection.LeftBottom:
                    // 左下角L - 横向部分
                    var leftBottomHorizontalRect = new RectangleGeometry(new Rect(0, 0, width, height));
                    var leftBottomHorizontalPathFigure = new PathFigure
                    {
                        StartPoint = new Point(6, 0),
                        Segments = new PathSegmentCollection
                        {
                            new ArcSegment(new Point(0, height), new Size(90, 90), 0, false, SweepDirection.Clockwise, true),
                            new LineSegment(new Point(0, 0), true)
                        }
                    };
                    var leftBottomHorizontalPathGeometry = new PathGeometry(new[] { leftBottomHorizontalPathFigure });
                    var leftBottomHorizontalCombined = new CombinedGeometry(GeometryCombineMode.Exclude, leftBottomHorizontalRect, leftBottomHorizontalPathGeometry);
                    LAngleGeometry = leftBottomHorizontalCombined;

                    // 左下角L - 垂直部分
                    var leftBottomVerticalRect = new RectangleGeometry(new Rect(0, 0, vwidth, vheight)); 
                    var leftBottomVerticalPathFigure = new PathFigure
                    {
                        StartPoint = new Point(vwidth, vheight - 6),
                        Segments = new PathSegmentCollection
                        {
                            new ArcSegment(new Point(vwidth - 6, vheight), new Size(90, 90), 0, false, SweepDirection.Counterclockwise, true),
                            new LineSegment(new Point(vwidth, vheight), true)
                        }
                    };
                    var leftBottomVerticalPathGeometry = new PathGeometry(new[] { leftBottomVerticalPathFigure });
                    var leftBottomVerticalCombined = new CombinedGeometry(GeometryCombineMode.Exclude, leftBottomVerticalRect, leftBottomVerticalPathGeometry);
                    VAngleGeometry = leftBottomVerticalCombined;
                    break;
            }
        }
    }
}


