﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TD.其他
{
    public class SystemConstant
    {
        #region IO设备 参数
        public static int IoSendTimeout = 500;  // IO设备发送超时
        public static int IoReceiveTimeout = 500;  // IO设备发送超时
        public static int IoMonitorCheckInterval = 200;  // IO设备状态查询间隔
        #endregion
    }
    public class SystemInfo
    {
       
    }
  
}
