# 管路图实现说明

## 概述
根据提供的图片，我们使用现有的用户控件重新设计了ChamberPage.xaml中的管路系统图，实现了以下功能：

## 使用的控件
1. **EllipseUserControl.xaml** - 门阀控件，用于表示各种阀门
2. **LAngleUserControl.xaml** - L角控件，用于管路转角连接
3. **PipeUserControl.xaml** - 管道控件，用于直线管路

## 主要组件布局

### 容器
- **TM容器**: 左侧主要容器 (100,200) 尺寸250x180
- **PM容器**: 右侧主要容器 (500,150) 尺寸250x280  
- **底部小容器**: 两个小容器用于底部连接

### 管路系统
1. **顶部主管路PU12**: 水平管路，长度400，连接整个系统顶部
2. **TM到PM连接**: 水平管路连接两个主要容器
3. **PM右侧分支**: 5个分支出口，分别对应：
   - PU11
   - PN8/PN7
   - PN2/PN1  
   - LLM4/LLM3
   - LLM6/LLM5

### 阀门布置
所有阀门都使用EllipseUserControl实现，并确保居中放置：
- **TP5阀门**: 顶部主管路中央 (343,43)
- **分子泵阀门**: TM左侧 (94,284)
- **连接阀门**: TM到PM之间 (419,284)
- **主出口阀门**: PM右侧主出口 (818,284)
- **分支阀门**: PM右侧各分支出口处
- **底部阀门**: 底部连接处 (294,534)
- **N2泵阀门**: 右下角 (994,534)

### L角连接
使用LAngleUserControl实现管路转角：
- **PU12左侧L角**: 连接到TM (LeftBottom方向)
- **PU12右侧L角**: 连接到PM (RightBottom方向)
- **PM右侧分支L角**: 5个RightTop方向的L角，连接各分支
- **底部连接L角**: 连接底部容器 (LeftBottom方向)

## 设计特点
1. **阀门居中**: 所有阀门都精确居中放置在对应管路上
2. **管道打通**: 使用连续的管路控件确保视觉上的连通效果
3. **响应式设计**: 使用Viewbox确保在不同屏幕尺寸下正确显示
4. **标签标识**: 添加了完整的标签系统，标识各个组件和参数

## 技术实现
- 使用Canvas布局实现精确定位
- 通过Panel.ZIndex确保阀门显示在管路之上
- 保持与Page1.xaml相同的设计风格和颜色方案
- 实现了完整的管路连通性和视觉效果

## 文件位置
主要实现文件：`Pages/ChamberPage.xaml`

该实现完全按照提供的图片要求，使用现有控件重新构建了管路系统图，确保了阀门居中和管道连通的视觉效果。
