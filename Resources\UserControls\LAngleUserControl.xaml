<UserControl x:Class="TD.Resources.UserControls.LAngleUserControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:TD.Resources.UserControls" xmlns:local1="clr-namespace:TD"
             mc:Ignorable="d" 
            >
        <Canvas >
        <!-- 横向部分 -->
        <Path StrokeThickness="0" Name="HorizontalPath"  
              Data="{Binding LAngleGeometry, RelativeSource={RelativeSource AncestorType=UserControl}}">
            <Path.Style>
                <Style TargetType="Path">
                    <Setter Property="Width" Value="{Binding HorizontalLength, RelativeSource={RelativeSource AncestorType=UserControl}}"/>
                    <Setter Property="Height" Value="8"/>
                    <Setter Property="Fill">
                        <Setter.Value>
                            <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                <GradientStop Color="#FF505050" Offset="0.0"/>
                                <GradientStop Color="#FF808080" Offset="0.1"/>
                                <GradientStop Color="#FFF0F0F0" Offset="0.45"/>
                                <GradientStop Color="#FFF0F0F0" Offset="0.55"/>
                                <GradientStop Color="#FF808080" Offset="0.9"/>
                                <GradientStop Color="#FF505050" Offset="1.0"/>
                            </LinearGradientBrush>
                        </Setter.Value>
                    </Setter>
                    <Style.Triggers>
                        <DataTrigger Binding="{Binding CornerDirection, RelativeSource={RelativeSource AncestorType=UserControl}}" Value="RightTop">
                            <Setter Property="Canvas.Left" Value="0"/>
                            <Setter Property="Canvas.Top" Value="0"/>
                        </DataTrigger>
                        <DataTrigger Binding="{Binding CornerDirection, RelativeSource={RelativeSource AncestorType=UserControl}}" Value="LeftTop">
                            <Setter Property="Canvas.Left" Value="0"/>
                            <Setter Property="Canvas.Top" Value="0"/>
                        </DataTrigger>
                        <DataTrigger Binding="{Binding CornerDirection, RelativeSource={RelativeSource AncestorType=UserControl}}" Value="RightBottom">
                            <Setter Property="Canvas.Left" Value="6"/>
                            <Setter Property="Canvas.Top" Value="{Binding HorizontalLength, RelativeSource={RelativeSource AncestorType=UserControl}}"/>
                        </DataTrigger>
                        <DataTrigger Binding="{Binding CornerDirection, RelativeSource={RelativeSource AncestorType=UserControl}}" Value="LeftBottom">
                            <Setter Property="Canvas.Left" Value="0"/>
                            <Setter Property="Canvas.Top" Value="{Binding VerticalLength, RelativeSource={RelativeSource AncestorType=UserControl}}"/>
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </Path.Style>
        </Path>

        <!-- 垂直部分 -->
        <Path StrokeThickness="0" Name="VerticalPath"
              Data="{Binding VAngleGeometry, RelativeSource={RelativeSource AncestorType=UserControl}}">
            <Path.Style>
                <Style TargetType="Path">
                    <Setter Property="Width" Value="8"/>
                    <Setter Property="Height" Value="{Binding VerticalLength, RelativeSource={RelativeSource AncestorType=UserControl}}"/>
                    <Setter Property="Fill">
                        <Setter.Value>
                            <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                                <GradientStop Color="#FF505050" Offset="0.0"/>
                                <GradientStop Color="#FF808080" Offset="0.1"/>
                                <GradientStop Color="#FFF0F0F0" Offset="0.45"/>
                                <GradientStop Color="#FFF0F0F0" Offset="0.55"/>
                                <GradientStop Color="#FF808080" Offset="0.9"/>
                                <GradientStop Color="#FF505050" Offset="1.0"/>
                            </LinearGradientBrush>
                        </Setter.Value>
                    </Setter>
                    <Style.Triggers>
                        <DataTrigger Binding="{Binding CornerDirection, RelativeSource={RelativeSource AncestorType=UserControl}}" Value="RightTop">
                            <Setter Property="Canvas.Left" Value="{Binding VerticalPipeLeft, RelativeSource={RelativeSource AncestorType=UserControl}}"/>
                            <Setter Property="Canvas.Top" Value="0"/>
                        </DataTrigger>
                        <DataTrigger Binding="{Binding CornerDirection, RelativeSource={RelativeSource AncestorType=UserControl}}" Value="LeftTop">
                            <Setter Property="Canvas.Left" Value="0"/>
                            <Setter Property="Canvas.Top" Value="0"/>
                        </DataTrigger>
                        <DataTrigger Binding="{Binding CornerDirection, RelativeSource={RelativeSource AncestorType=UserControl}}" Value="RightBottom">
                            <Setter Property="Canvas.Left" Value="{Binding HorizontalLength, RelativeSource={RelativeSource AncestorType=UserControl}}"/>
                            <Setter Property="Canvas.Top" Value="6"/>
                        </DataTrigger>
                        <DataTrigger Binding="{Binding CornerDirection, RelativeSource={RelativeSource AncestorType=UserControl}}" Value="LeftBottom">
                            <Setter Property="Canvas.Left" Value="0"/>
                            <Setter Property="Canvas.Top" Value="6"/>
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </Path.Style>
          
        </Path>
    </Canvas>
</UserControl>
