{"format": 1, "restore": {"D:\\001中科芯微项目\\01XM\\其他测试使用\\TD-4027 采集模块\\串口读取数据\\串口读取数据\\TD.csproj": {}}, "projects": {"D:\\001中科芯微项目\\01XM\\其他测试使用\\TD-4027 采集模块\\串口读取数据\\串口读取数据\\TD.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\001中科芯微项目\\01XM\\其他测试使用\\TD-4027 采集模块\\串口读取数据\\串口读取数据\\TD.csproj", "projectName": "TD", "projectPath": "D:\\001中科芯微项目\\01XM\\其他测试使用\\TD-4027 采集模块\\串口读取数据\\串口读取数据\\TD.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\001中科芯微项目\\01XM\\其他测试使用\\TD-4027 采集模块\\串口读取数据\\串口读取数据\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"CommunityToolkit.Mvvm": {"target": "Package", "version": "[8.4.0, )"}, "System.IO.Ports": {"target": "Package", "version": "[9.0.6, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.303/PortableRuntimeIdentifierGraph.json"}}}}}